import request from 'supertest';
import { Test } from '@nestjs/testing';
import { helloWordController } from '../src/module/app/controller';
import { helloWordService } from '../src/module/app/service';

describe('AppController (e2e)', () => {
    let app;

    beforeAll(async () => {
        const moduleFixture = await Test.createTestingModule({
            controllers: [helloWordController],
            providers: [helloWordService],
        }).compile();

        app = moduleFixture.createNestApplication();
        await app.init();
    });

    it('/GET /', () => {
        return request(app.getHttpServer())
            .get('/')
            .expect(200)
            .expect('Hello World!');
    });
});
