/**
 * 应用根模块
 * 
 * - 可以引入其他模块，形成树状结构
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { config } from '../src/config/index';
import { helloWordModule } from './modules/helloWord/module';

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            cache: true,
            load: [config]
        }),
        helloWordModule
    ],
})
export class AppModule {}
