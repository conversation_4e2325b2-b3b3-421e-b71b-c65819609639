{"name": "fly_disk_service_center", "version": "1.0.0", "description": "NAS 服务中心", "author": "Flying Fish Studio", "private": true, "scripts": {"format": "prettier --write \"**/*.js\"", "start": "babel-node app.js", "start:dev": "nodemon", "test": "jest", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.4.6", "@nestjs/platform-express": "^10.4.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.5.5"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/eslint-parser": "^7.28.0", "@babel/node": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.18.6", "@babel/plugin-transform-runtime": "^7.18.6", "@babel/preset-env": "^7.18.6", "@babel/register": "^7.18.6", "@babel/runtime": "^7.18.6", "@eslint/js": "^9.32.0", "@nestjs/testing": "^10.0.0", "eslint": "^9.32.0", "globals": "^16.3.0", "jest": "^28.1.2", "nodemon": "^2.0.19", "prettier": "^2.7.1", "supertest": "^6.2.4"}, "engines": {"node": ">=22"}, "jest": {"moduleFileExtensions": ["js", "json"], "rootDir": "src", "testRegex": ".spec.js$", "coverageDirectory": "../coverage"}}